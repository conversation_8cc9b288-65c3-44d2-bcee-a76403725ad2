'use client'

import { useStore } from '@/app/(panel)/members/teams/stores/store'
import EmptyState from '@/assets/vectors/empty-states/09.svg'
import EmptyStateNoRecord from '@/assets/vectors/empty-states/13.svg'
import ButtonCustom from '@/components/custom-ui/button-custom'
import Loading from '@/components/display/Loading'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { useUser } from '@/lib/hooks/useUser'
import { queryFetchHelper } from '@/lib/utils/fetchHelper'
import { useQuery } from '@tanstack/react-query'
import dynamic from 'next/dynamic'
import Image from 'next/image'
import { useRef, useState } from 'react'
import { FaExclamationCircle } from 'react-icons/fa'
import { FiPlusCircle } from 'react-icons/fi'
import { useDebounceCallback } from 'usehooks-ts'

import AddMemberModalIndex from '@/app/(panel)/members/teams/components/modals/add-member'
import AddTeamModal from '@/app/(panel)/members/teams/components/modals/add-team'
import DeleteTeamModal from '@/app/(panel)/members/teams/components/modals/delete-team'
import { buildQueryString } from '@/lib/utils'
import NiceModal from '@ebay/nice-modal-react'

NiceModal.register('addTeam', AddTeamModal)
NiceModal.register('deleteTeam', DeleteTeamModal)
NiceModal.register('addTeamMemberIndex', AddMemberModalIndex)

const RowLayout = dynamic(() => import('@/app/(panel)/members/teams/displays/row'), {
  ssr: true,
  loading: () => <Loading />,
})

export default function Component() {
  const { refetchFlag, refetchTrigger, pagination } = useStore()
  const { user } = useUser()

  const searchRef = useRef<HTMLInputElement>(null)
  const [search, setSearch] = useState('')
  const debounced = useDebounceCallback(setSearch, 500)
  const clearSearch = () => {
    if (searchRef.current) {
      searchRef.current.value = ''
    }
    setSearch('')
  }

  const { isPending, error, data } = useQuery({
    queryKey: ['getTeams', refetchFlag, search, pagination, user?.current_organization?.alias],
    queryFn: async (): Promise<ApiResponseWithDataPaginationField> => {
      if (!user?.current_organization?.alias) {
        throw new Error('No organization context available')
      }

      const queryString = buildQueryString({
        filter: {
          name: search,
        },
        page: pagination.pageIndex + 1 + '',
        per_page: pagination.pageSize + '',
      })

      return await queryFetchHelper(`/${user.current_organization.alias}/teams?${queryString}`)
    },
    enabled: !!user?.current_organization?.alias,
  })

  const showModal = (name: string) => {
    NiceModal.show(name, {
      callback: () => {
        clearSearch()
        refetchTrigger()
      },
    }).then()
  }

  const renderBodyContent = () => {
    if (isPending) {
      return <Loading />
    }

    if (error) {
      return (
        <Alert variant="destructive">
          <FaExclamationCircle className="h-4 w-4" />
          <AlertTitle>Lỗi!</AlertTitle>
          <AlertDescription className="text-xs">Không thể tải danh sách teams. Vui lòng thử lại sau.</AlertDescription>
        </Alert>
      )
    }

    if (!data?.data?.data?.length && !search) {
      return (
        <div className="flex min-h-96 flex-col items-center justify-center gap-6 text-center">
          <Image
            src={EmptyState}
            alt="Teams"
            width={300}
            priority={true}
          />
          <div>
            Chưa có team nào.
            <br />
            Tạo team đầu tiên của bạn ngay bây giờ
          </div>
          <Button
            variant="outline"
            size="lg"
            onClick={() => showModal('addTeam')}>
            <FiPlusCircle className="mr-2 h-4 w-4" />
            Thêm Team
          </Button>
        </div>
      )
    }

    if (!data?.data?.data?.length) {
      return (
        <div className="flex min-h-96 flex-col items-center justify-center gap-6 text-center">
          <Image
            src={EmptyStateNoRecord}
            alt="No Teams"
            width={300}
            priority={true}
          />
          <div className="flex max-w-sm flex-col gap-2">
            <strong>Không tìm thấy team nào.</strong>
            <p>
              Tìm kiếm &quot;{search}&quot; không khớp với team nào.
              <br />
              Vui lòng thử lại.
            </p>
          </div>
          <div className="flex gap-4">
            <Button
              variant="outline"
              size="lg"
              onClick={() => clearSearch()}>
              Xóa tìm kiếm
            </Button>
            <Button
              size="lg"
              onClick={() => showModal('addTeam')}>
              <FiPlusCircle className="mr-2 h-4 w-4" />
              Team Mới
            </Button>
          </div>
        </div>
      )
    }

    return <RowLayout apiData={data} />
  }

  return (
    <div className="space-y-4">
      <div className="flex flex-col justify-between space-y-2 md:flex-row md:items-center">
        <div>
          <h3 className="text-lg font-semibold">Danh sách Teams</h3>
          <p className="text-muted-foreground text-sm">Quản lý các teams trong tổ chức của bạn.</p>
        </div>
        <div className="flex items-center justify-between space-x-2">
          <Input
            ref={searchRef}
            type="search"
            placeholder="Tìm kiếm teams..."
            className="w-[200px]"
            defaultValue={search}
            onChange={(event) => debounced(event.target.value)}
          />
          <ButtonCustom
            md="icon"
            lg="default"
            onClick={() => showModal('addTeam')}>
            <FiPlusCircle className="h-4 w-4 md:mr-2" />
            <span className="hidden md:block">Thêm Team</span>
          </ButtonCustom>
        </div>
      </div>
      {renderBodyContent()}
    </div>
  )
}
