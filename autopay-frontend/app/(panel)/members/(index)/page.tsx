'use client'

import { DataTablePagination } from '@/app/(panel)/members/(index)/components/data-table-pagination'
import { useStore } from '@/app/(panel)/members/(index)/stores/store'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Input } from '@/components/ui/input'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { useUser } from '@/lib/hooks/useUser'
import { buildQueryString, cn } from '@/lib/utils'
import { queryFetchHelper } from '@/lib/utils/fetchHelper'
import { useQuery } from '@tanstack/react-query'
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table'
import crypto from 'crypto'
import { formatDistanceToNow } from 'date-fns'
import { vi } from 'date-fns/locale'
import { Crown, Eye, MoreHorizontal, Settings, UserMinus } from 'lucide-react'
import { useEffect, useState } from 'react'
import { FaExclamationCircle } from 'react-icons/fa'
import { FiPlusCircle } from 'react-icons/fi'
import { useDebounceCallback, useWindowSize } from 'usehooks-ts'

// Function to generate Gravatar URL
const getGravatarUrl = (email: string, size: number = 80): string => {
  const hash = crypto.createHash('md5').update(email.toLowerCase().trim()).digest('hex')
  return `https://www.gravatar.com/avatar/${hash}?s=${size}&d=identicon`
}

// Member type definition
interface Member {
  id: string
  name: string
  email: string
  is_owner?: boolean
  is_active?: boolean
  status?: string
  joined_at?: string
  roles_count?: number
  teams_count?: number
}

// Define columns for the datatable
const columns: ColumnDef<Member>[] = [
  {
    accessorKey: 'name',
    header: 'Thành viên',
    cell: ({ row }) => {
      const member = row.original
      return (
        <div className="flex items-center space-x-3">
          <Avatar className="h-8 w-8">
            <AvatarImage
              src={getGravatarUrl(member.email, 32)}
              alt={member.name}
            />
            <AvatarFallback>
              {member.name
                .split(' ')
                .map((n) => n[0])
                .join('')
                .toUpperCase()}
            </AvatarFallback>
          </Avatar>
          <div className="flex flex-col">
            <div className="flex items-center space-x-2">
              <span className="font-medium">{member.name}</span>
              {member.is_owner && <Crown className="h-4 w-4 text-yellow-500" />}
            </div>
            <span className="text-muted-foreground text-sm">{member.email}</span>
          </div>
        </div>
      )
    },
  },
  {
    accessorKey: 'teams_count',
    header: 'Teams',
    cell: ({ row }) => {
      const count = row.getValue('teams_count') as number
      return (
        <Badge variant="secondary">
          {count || 0} team{count !== 1 ? 's' : ''}
        </Badge>
      )
    },
  },
  {
    accessorKey: 'roles_count',
    header: 'Vai trò',
    cell: ({ row }) => {
      const count = row.getValue('roles_count') as number
      return <Badge variant="outline">{count || 0} vai trò</Badge>
    },
  },
  {
    accessorKey: 'status',
    header: 'Trạng thái',
    cell: ({ row }) => {
      const status = row.getValue('status') as string
      const isActive = status === 'active'
      return <Badge variant={isActive ? 'default' : 'secondary'}>{isActive ? 'Hoạt động' : 'Không hoạt động'}</Badge>
    },
  },
  {
    accessorKey: 'joined_at',
    header: 'Tham gia',
    cell: ({ row }) => {
      const joinedAt = row.getValue('joined_at') as string
      if (!joinedAt) return <span className="text-muted-foreground">-</span>

      try {
        const date = new Date(joinedAt)
        return <span className="text-sm">{formatDistanceToNow(date, { addSuffix: true, locale: vi })}</span>
      } catch {
        return <span className="text-muted-foreground">-</span>
      }
    },
  },
  {
    id: 'actions',
    header: () => <div className="text-right"></div>,
    cell: () => {
      return (
        <div className="text-right">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem>
                <Eye className="mr-2 size-4" />
                Xem chi tiết
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Settings className="mr-2 size-4" />
                Quản lý vai trò
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem className="text-red-600">
                <UserMinus className="mr-2 size-4" />
                Xóa khỏi tổ chức
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      )
    },
  },
]

export default function MembersPage() {
  const { pagination, setPagination } = useStore()
  const { user } = useUser()
  const [search, setSearch] = useState('')
  const debounced = useDebounceCallback(setSearch, 500)

  const [rowSelection, setRowSelection] = useState({})
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({})
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([])
  const [sorting, setSorting] = useState<SortingState>([])
  const { width = 0 } = useWindowSize()
  const isSmallScreen = width < 768

  // Hide columns on mobile if needed
  useEffect(() => {
    setColumnVisibility({
      teams_count: !isSmallScreen,
      roles_count: !isSmallScreen,
    })
  }, [isSmallScreen, width])

  const { isPending, error, data } = useQuery({
    queryKey: ['getOrganizationMembers', search, pagination, user?.current_organization?.alias],
    queryFn: async (): Promise<ApiResponseWithDataPaginationField> => {
      if (!user?.current_organization?.alias) {
        throw new Error('No organization context available')
      }

      const queryString = buildQueryString({
        filter: {
          search: search,
        },
        page: pagination.pageIndex + 1 + '',
        per_page: pagination.pageSize + '',
      })

      return await queryFetchHelper(`/${user.current_organization.alias}/members?${queryString}`)
    },
    enabled: !!user?.current_organization?.alias,
  })

  if (error) {
    return (
      <div className="space-y-4">
        <div>
          <h3 className="text-lg font-semibold">Quản lý thành viên</h3>
          <p className="text-muted-foreground text-sm">Quản lý thành viên trong tất cả các teams.</p>
        </div>
        <Alert variant="destructive">
          <FaExclamationCircle className="h-4 w-4" />
          <AlertTitle>Lỗi!</AlertTitle>
          <AlertDescription>Không thể tải danh sách thành viên. Vui lòng thử lại sau.</AlertDescription>
        </Alert>
      </div>
    )
  }

  const members = data?.data?.data || []
  const {
    data: { last_page: pageCount, total },
  } = data || { data: { last_page: 1, total: 0 } }

  const table = useReactTable({
    data: members,
    columns,
    state: {
      sorting,
      columnVisibility,
      rowSelection,
      columnFilters,
      pagination,
    },
    manualPagination: true,
    onPaginationChange: setPagination,
    pageCount: pageCount,
    rowCount: total,
    enableRowSelection: true,
    onRowSelectionChange: setRowSelection,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
  })

  return (
    <div className="space-y-4">
      <div className="flex flex-col justify-between space-y-2 md:flex-row md:items-center">
        <div>
          <h3 className="text-lg font-semibold">Quản lý thành viên</h3>
          <p className="text-muted-foreground text-sm">Quản lý thành viên trong tất cả các teams.</p>
        </div>
        <div className="flex items-center space-x-2">
          <Input
            type="search"
            placeholder="Tìm kiếm thành viên..."
            className="w-[200px]"
            value={search}
            onChange={(event) => debounced(event.target.value)}
          />
          <Button>
            <FiPlusCircle className="mr-2 size-4" />
            Thêm thành viên
          </Button>
        </div>
      </div>

      <div className="space-y-4">
        <div className="rounded-lg border">
          <Table>
            <TableHeader className="bg-accent">
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header, index) => {
                    return (
                      <TableHead
                        key={header.id}
                        colSpan={header.colSpan}
                        className={cn({
                          'rounded-tl-lg': index === 0,
                          'rounded-tr-lg': index === headerGroup.headers.length - 1,
                        })}>
                        {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                      </TableHead>
                    )
                  })}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {isPending ? (
                <TableRow>
                  <TableCell
                    colSpan={columns.length}
                    className="h-24 text-center">
                    Đang tải...
                  </TableCell>
                </TableRow>
              ) : table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow
                    key={row.id}
                    data-state={row.getIsSelected() && 'selected'}>
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id}>{flexRender(cell.column.columnDef.cell, cell.getContext())}</TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell
                    colSpan={columns.length}
                    className="h-24 text-center">
                    {search
                      ? `Không tìm thấy thành viên với từ khóa "${search}"`
                      : 'Chưa có thành viên nào trong tổ chức'}
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
        <div className="flex items-center justify-end">
          <DataTablePagination table={table} />
        </div>
      </div>
    </div>
  )
}
